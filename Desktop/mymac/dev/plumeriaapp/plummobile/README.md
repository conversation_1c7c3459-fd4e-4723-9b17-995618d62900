# Plumeria Mobile - Construction Management App

A Flutter-based construction management mobile application with dynamic branding capabilities and role-based access control.

## Features

### 🎨 Dynamic Branding System
- API-driven app customization (colors, logos, text)
- Real-time theme updates without app restart
- Multi-client support with configurable branding
- Fallback to default configuration when offline

### 🔐 Authentication & Security
- Mobile number + password authentication
- OTP-based login fallback
- JWT token management with auto-refresh
- Secure local storage for sensitive data
- Role-based access control (RBAC)

### 📱 Core Functionality
- **Dashboard**: Role-specific home screen with quick actions
- **Attendance Management**: Check-in/out with location tracking
- **Profile Management**: User profile viewing and editing
- **Admin Panel**: User management and system configuration
- **Permission System**: Granular access control

### 🏗️ Construction-Specific Features
- Staff management and role assignment
- Project-based access control
- Attendance tracking and reporting
- Admin tools for system management

## Technical Architecture

### State Management
- **Provider Pattern**: For app-wide state management
- **BrandingProvider**: Handles dynamic theming and configuration
- **AuthProvider**: Manages authentication state and user sessions
- **StaffProvider**: Handles staff-related data and operations

### API Integration
- **RESTful API**: Backend communication via Dio HTTP client
- **Interceptors**: Automatic token injection and error handling
- **Offline Support**: Cached branding and graceful degradation
- **Base URL**: `http://localhost:3000/api/mobile/v1`

### Security
- **Flutter Secure Storage**: For sensitive data (tokens, credentials)
- **SharedPreferences**: For app configuration and cache
- **Token Management**: Automatic refresh and secure storage
- **Permission Guards**: Route-level access control

## Project Structure

```
lib/
├── core/
│   ├── app_routes.dart          # Navigation routes
│   └── app_theme.dart           # Theme configuration
├── models/
│   ├── auth_model.dart          # Authentication data models
│   ├── branding_model.dart      # Branding configuration models
│   └── staff_model.dart         # Staff/user data models
├── providers/
│   ├── auth_provider.dart       # Authentication state management
│   ├── branding_provider.dart   # Branding state management
│   └── staff_provider.dart      # Staff data management
├── screens/
│   ├── auth/                    # Authentication screens
│   ├── dashboard/               # Main dashboard and tabs
│   ├── profile/                 # User profile screens
│   ├── admin/                   # Admin panel screens
│   └── splash_screen.dart       # App initialization screen
├── services/
│   ├── api_service.dart         # HTTP client and API communication
│   ├── auth_service.dart        # Authentication API calls
│   ├── branding_service.dart    # Branding API and caching
│   ├── permission_service.dart  # Role-based access control
│   └── storage_service.dart     # Local data storage
├── utils/
│   ├── device_utils.dart        # Device information utilities
│   └── route_guard.dart         # Navigation guards
├── widgets/
│   └── access_denied_widget.dart # Permission-based UI components
└── main.dart                    # App entry point
```

## Getting Started

### Prerequisites
- Flutter SDK (>=3.7.2)
- Dart SDK
- Android Studio / VS Code
- iOS development tools (for iOS builds)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Kalsodigital/plumeriamobile.git
   cd plumeriamobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoint**
   Update the base URL in `lib/services/api_service.dart`:
   ```dart
   static const String baseUrl = 'YOUR_API_ENDPOINT';
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Build for Production

**Android:**
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

**iOS:**
```bash
flutter build ios --release
```

## Configuration

### API Endpoints
The app expects the following API endpoints:

- `POST /auth/login` - User authentication
- `POST /auth/otp/send` - Send OTP
- `POST /auth/otp/verify` - Verify OTP
- `GET /auth/profile` - Get user profile
- `GET /branding` - Get branding configuration
- `PUT /branding` - Update branding (admin only)

### Branding Configuration
The app supports dynamic branding through API configuration:

```json
{
  "appName": "Your App Name",
  "companyName": "Your Company",
  "tagline": "Your Tagline",
  "theme": {
    "primaryColor": "#667eea",
    "secondaryColor": "#764ba2",
    "accentColor": "#4CAF50",
    "errorColor": "#FF5722",
    "backgroundColor": "#F8FAFC"
  },
  "assets": {
    "logoUrl": "https://your-domain.com/logo.png",
    "appIconUrl": "https://your-domain.com/icon.png"
  },
  "features": {
    "showRegistration": false,
    "enableSocialLogin": false,
    "enableBiometric": true,
    "showForgotPassword": true
  }
}
```

## Dependencies

### Core Dependencies
- `flutter`: Flutter SDK
- `provider`: State management
- `dio`: HTTP client
- `shared_preferences`: Local storage
- `flutter_secure_storage`: Secure storage
- `device_info_plus`: Device information

### Development Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Code analysis

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary software developed for construction management purposes.

## Support

For support and questions, please contact the development team.
