import 'package:flutter/foundation.dart';
import '../models/staff_model.dart';
import '../services/auth_service.dart';

class StaffProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  Staff? _staff;
  bool _isLoading = false;
  String? _error;

  // Getters
  Staff? get staff => _staff;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.updateProfile(profileData);
      
      if (response.success) {
        // Refresh staff data after update
        await getProfile();
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Get updated profile
  Future<bool> getProfile() async {
    try {
      final response = await _authService.getProfile();
      
      if (response.success && response.data != null) {
        _staff = Staff.fromJson(response.data);
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword, String confirmPassword) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.changePassword(currentPassword, newPassword, confirmPassword);
      
      if (response.success) {
        _setLoading(false);
        return true;
      } else {
        _setError(response.message);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Set staff data (called from auth provider)
  void setStaff(Staff staff) {
    _staff = staff;
    notifyListeners();
  }

  // Clear staff data
  void clearStaff() {
    _staff = null;
    _clearError();
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}