import 'package:flutter/material.dart';
import '../services/permission_service.dart';

/// Widget displayed when user doesn't have sufficient permissions
class AccessDeniedWidget extends StatelessWidget {
  final String message;
  final String? requiredPermission;
  final List<String>? requiredPermissions;
  final List<String>? anyOfPermissions;
  final String? requiredRole;
  final bool showCurrentRole;
  final bool showContactAdmin;

  const AccessDeniedWidget({
    super.key,
    required this.message,
    this.requiredPermission,
    this.requiredPermissions,
    this.anyOfPermissions,
    this.requiredRole,
    this.showCurrentRole = true,
    this.showContactAdmin = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.block,
                    size: 60,
                    color: Colors.red.shade400,
                  ),
                ),
                const SizedBox(height: 32),

                // Title
                Text(
                  'Access Denied',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Message
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Current user info
                if (showCurrentRole && PermissionService.currentStaff != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Current Access:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.blue.shade800,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Role: ${PermissionService.getCurrentRole() ?? 'No role assigned'}',
                          style: TextStyle(color: Colors.blue.shade700),
                        ),
                        if (PermissionService.getCurrentPermissions().isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            'Permissions: ${PermissionService.getCurrentPermissions().length}',
                            style: TextStyle(color: Colors.blue.shade700),
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Required permissions info
                if (_hasRequirements()) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Required Access:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade800,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._buildRequirements(),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Contact admin message
                if (showContactAdmin) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.green.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Contact your administrator to request the necessary permissions.',
                            style: TextStyle(
                              color: Colors.green.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                ],

                // Back button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Go Back',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _hasRequirements() {
    return requiredPermission != null ||
        (requiredPermissions != null && requiredPermissions!.isNotEmpty) ||
        (anyOfPermissions != null && anyOfPermissions!.isNotEmpty) ||
        requiredRole != null;
  }

  List<Widget> _buildRequirements() {
    final List<Widget> widgets = [];

    if (requiredRole != null) {
      widgets.add(
        Text(
          'Role: $requiredRole',
          style: TextStyle(color: Colors.orange.shade700),
        ),
      );
    }

    if (requiredPermission != null) {
      widgets.add(
        Text(
          'Permission: $requiredPermission',
          style: TextStyle(color: Colors.orange.shade700),
        ),
      );
    }

    if (requiredPermissions != null && requiredPermissions!.isNotEmpty) {
      widgets.add(
        Text(
          'All Permissions:',
          style: TextStyle(
            color: Colors.orange.shade700,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
      for (final permission in requiredPermissions!) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              '• $permission',
              style: TextStyle(color: Colors.orange.shade700),
            ),
          ),
        );
      }
    }

    if (anyOfPermissions != null && anyOfPermissions!.isNotEmpty) {
      widgets.add(
        Text(
          'Any of these Permissions:',
          style: TextStyle(
            color: Colors.orange.shade700,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
      for (final permission in anyOfPermissions!) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              '• $permission',
              style: TextStyle(color: Colors.orange.shade700),
            ),
          ),
        );
      }
    }

    return widgets;
  }
}

/// Simple access denied widget for inline use
class InlineAccessDeniedWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final Color? color;

  const InlineAccessDeniedWidget({
    super.key,
    this.message,
    this.icon,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: (color ?? Colors.red).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: (color ?? Colors.red).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon ?? Icons.block,
            color: color ?? Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message ?? 'Access denied',
              style: TextStyle(
                color: color ?? Colors.red,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}