import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';

import 'core/app_routes.dart';
import 'providers/auth_provider.dart';
import 'providers/staff_provider.dart';
import 'providers/branding_provider.dart';
import 'screens/splash_screen.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const PlumeriaApp());
}

class PlumeriaApp extends StatelessWidget {
  const PlumeriaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => BrandingProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => StaffProvider()),
      ],
      child: Consumer<BrandingProvider>(
        builder: (context, brandingProvider, child) {
          return MaterialApp(
            title: brandingProvider.appTitle,
            theme: brandingProvider.generateThemeData(),
            darkTheme: brandingProvider.generateDarkThemeData(),
            themeMode: ThemeMode.system,
            home: const SplashScreen(),
            routes: AppRoutes.routes,
            onGenerateRoute: AppRoutes.onGenerateRoute,
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
