import '../models/staff_model.dart';

/// Service for handling role-based access control (RBAC) in the mobile app
class PermissionService {
  static Staff? _currentStaff;

  /// Set the current authenticated staff
  static void setCurrentStaff(Staff? staff) {
    _currentStaff = staff;
  }

  /// Get the current authenticated staff
  static Staff? get currentStaff => _currentStaff;

  /// Clear the current staff (on logout)
  static void clearCurrentStaff() {
    _currentStaff = null;
  }

  /// Check if user has a specific permission
  static bool hasPermission(String permissionSlug) {
    if (_currentStaff == null) return false;
    return _currentStaff!.hasPermission(permissionSlug);
  }

  /// Check if user has any of the specified permissions
  static bool hasAnyPermission(List<String> permissionSlugs) {
    if (_currentStaff == null) return false;
    return _currentStaff!.hasAnyPermission(permissionSlugs);
  }

  /// Check if user has all of the specified permissions
  static bool hasAllPermissions(List<String> permissionSlugs) {
    if (_currentStaff == null) return false;
    return _currentStaff!.hasAllPermissions(permissionSlugs);
  }

  /// Check if user has a specific role
  static bool hasRole(String roleName) {
    if (_currentStaff == null) return false;
    return _currentStaff!.hasRole(roleName);
  }

  /// Check if user is admin (has admin role)
  static bool isAdmin() {
    return hasRole('Admin') || hasRole('Super Admin') || hasRole('admin');
  }

  /// Check if user is manager (has manager role)
  static bool isManager() {
    return hasRole('Manager') || hasRole('manager') || isAdmin();
  }

  /// Check if user is staff (has staff role)
  static bool isStaff() {
    return hasRole('Staff') || hasRole('staff') || isManager();
  }

  /// Get current user's role name
  static String? getCurrentRole() {
    if (_currentStaff?.role == null) return null;
    return _currentStaff!.role!.name;
  }

  /// Get current user's permissions
  static List<Permission> getCurrentPermissions() {
    if (_currentStaff == null) return [];
    return _currentStaff!.permissions;
  }

  /// Get permission slugs as list
  static List<String> getCurrentPermissionSlugs() {
    return getCurrentPermissions().map((p) => p.slug).toList();
  }

  /// Check if user can access admin features
  static bool canAccessAdminFeatures() {
    return hasAnyPermission([
      'admin.view',
      'admin.manage',
      'users.manage',
      'roles.manage',
      'permissions.manage'
    ]);
  }

  /// Check if user can manage staff
  static bool canManageStaff() {
    return hasAnyPermission([
      'staff.create',
      'staff.edit',
      'staff.delete',
      'staff.manage'
    ]);
  }

  /// Check if user can view staff
  static bool canViewStaff() {
    return hasAnyPermission([
      'staff.view',
      'staff.list',
      'staff.manage'
    ]) || canManageStaff();
  }

  /// Check if user can manage attendance
  static bool canManageAttendance() {
    return hasAnyPermission([
      'attendance.create',
      'attendance.edit',
      'attendance.delete',
      'attendance.manage'
    ]);
  }

  /// Check if user can view attendance
  static bool canViewAttendance() {
    return hasAnyPermission([
      'attendance.view',
      'attendance.list',
      'attendance.manage'
    ]) || canManageAttendance();
  }

  /// Check if user can manage projects
  static bool canManageProjects() {
    return hasAnyPermission([
      'projects.create',
      'projects.edit',
      'projects.delete',
      'projects.manage'
    ]);
  }

  /// Check if user can view projects
  static bool canViewProjects() {
    return hasAnyPermission([
      'projects.view',
      'projects.list',
      'projects.manage'
    ]) || canManageProjects();
  }

  /// Check if user can access reports
  static bool canAccessReports() {
    return hasAnyPermission([
      'reports.view',
      'reports.generate',
      'reports.export'
    ]);
  }

  /// Check if user can access financial data
  static bool canAccessFinancials() {
    return hasAnyPermission([
      'financials.view',
      'financials.manage',
      'salary.view',
      'salary.manage'
    ]);
  }

  /// Check if user can access quick actions (check in/out)
  static bool canAccessQuickActions() {
    // Allow all roles to access quick actions
    if (hasAnyRole([
      'Project Manager',  // ID: 3 in your DB
      'manager',          // ID: 2 in your DB  
      'admin',            // ID: 1 in your DB
      'Account',          // ID: 10 in your DB
      'Site Engineer'     // Now allowed to see Quick Actions
    ])) {
      return true;
    }
    
    // Allow any other roles by default (more permissive approach)
    return true;
  }

  /// Helper method to check if user has any of the specified roles
  static bool hasAnyRole(List<String> roleNames) {
    if (_currentStaff?.role == null) return false;
    return roleNames.contains(_currentStaff!.role!.name);
  }

  /// Debug method to print current user permissions
  static void debugPermissions() {
    if (_currentStaff == null) {
      print('No staff logged in');
      return;
    }

    print('Staff: ${_currentStaff!.name}');
    print('Role: ${_currentStaff!.role?.name ?? 'No role'}');
    print('Permissions:');
    for (final permission in _currentStaff!.permissions) {
      print('  - ${permission.name} (${permission.slug})');
    }
  }
}

/// Common permission constants
class Permissions {
  // Admin permissions
  static const String adminView = 'admin.view';
  static const String adminManage = 'admin.manage';
  
  // User permissions
  static const String usersView = 'users.view';
  static const String usersManage = 'users.manage';
  
  // Staff permissions
  static const String staffView = 'staff.view';
  static const String staffCreate = 'staff.create';
  static const String staffEdit = 'staff.edit';
  static const String staffDelete = 'staff.delete';
  static const String staffManage = 'staff.manage';
  
  // Attendance permissions
  static const String attendanceView = 'attendance.view';
  static const String attendanceCreate = 'attendance.create';
  static const String attendanceEdit = 'attendance.edit';
  static const String attendanceDelete = 'attendance.delete';
  static const String attendanceManage = 'attendance.manage';
  
  // Project permissions
  static const String projectsView = 'projects.view';
  static const String projectsCreate = 'projects.create';
  static const String projectsEdit = 'projects.edit';
  static const String projectsDelete = 'projects.delete';
  static const String projectsManage = 'projects.manage';
  
  // Report permissions
  static const String reportsView = 'reports.view';
  static const String reportsGenerate = 'reports.generate';
  static const String reportsExport = 'reports.export';
  
  // Financial permissions
  static const String financialsView = 'financials.view';
  static const String financialsManage = 'financials.manage';
  static const String salaryView = 'salary.view';
  static const String salaryManage = 'salary.manage';
  
  // Role permissions
  static const String rolesView = 'roles.view';
  static const String rolesManage = 'roles.manage';
  
  // Permission permissions
  static const String permissionsView = 'permissions.view';
  static const String permissionsManage = 'permissions.manage';
}

/// Common role constants
class Roles {
  static const String admin = 'Admin';
  static const String superAdmin = 'Super Admin';
  static const String manager = 'Manager';
  static const String staff = 'Staff';
  static const String hr = 'HR';
  static const String accountant = 'Accountant';
  static const String supervisor = 'Supervisor';
}