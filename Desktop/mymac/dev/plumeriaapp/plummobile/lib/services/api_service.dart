import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api/mobile/v1';

  late Dio _dio;
  final StorageService _storageService = StorageService();

  ApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Request interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add authentication token if available
          final tokens = await _storageService.getAuthTokens();
          if (tokens != null) {
            options.headers['Authorization'] =
                '${tokens.tokenType} ${tokens.accessToken}';
          }

          // Add device info headers
          options.headers['User-Agent'] = await _getUserAgent();
          options.headers['X-App-Version'] = '1.0.0';
          options.headers['X-Platform'] =
              Platform.isAndroid ? 'android' : 'ios';

          if (kDebugMode) {
            print('🚀 REQUEST: ${options.method} ${options.uri}');
            print('📤 Headers: ${options.headers}');
            if (options.data != null) {
              print('📤 Body: ${options.data}');
            }
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          if (kDebugMode) {
            print(
              '✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}',
            );
            print('📥 Data: ${response.data}');
          }
          handler.next(response);
        },
        onError: (error, handler) {
          if (kDebugMode) {
            print(
              '❌ ERROR: ${error.response?.statusCode} ${error.requestOptions.uri}',
            );
            print('❌ Message: ${error.message}');
            print('❌ Data: ${error.response?.data}');
          }

          // Handle token expiration
          if (error.response?.statusCode == 401) {
            _handleUnauthorized();
          }

          handler.next(error);
        },
      ),
    );
  }

  Future<String> _getUserAgent() async {
    return 'PlumeriaStaff/1.0.0 (${Platform.operatingSystem})';
  }

  void _handleUnauthorized() {
    // Clear stored tokens on 401
    _storageService.clearAuthData();
    // You might want to navigate to login screen here
    // This would require passing a navigator context or using a global navigator
  }

  // GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // POST request
  Future<Response> post(String path, dynamic data) async {
    try {
      final response = await _dio.post(path, data: data);
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PUT request
  Future<Response> put(
    String path,
    dynamic data, {
    Map<String, String>? headers,
  }) async {
    try {
      final options = Options();
      if (headers != null) {
        options.headers = {..._dio.options.headers, ...headers};
      }
      final response = await _dio.put(path, data: data, options: options);
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // DELETE request
  Future<Response> delete(String path) async {
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PATCH request
  Future<Response> patch(String path, dynamic data) async {
    try {
      final response = await _dio.patch(path, data: data);
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return Exception(
          'Connection timeout. Please check your internet connection.',
        );
      case DioExceptionType.sendTimeout:
        return Exception('Request timeout. Please try again.');
      case DioExceptionType.receiveTimeout:
        return Exception('Response timeout. Please try again.');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Unknown error occurred';

        switch (statusCode) {
          case 400:
            return Exception('Bad request: $message');
          case 401:
            return Exception('Unauthorized: Please login again');
          case 403:
            return Exception('Forbidden: $message');
          case 404:
            return Exception('Not found: $message');
          case 429:
            return Exception('Too many requests: Please try again later');
          case 500:
            return Exception('Server error: Please try again later');
          default:
            return Exception('Request failed: $message');
        }
      case DioExceptionType.cancel:
        return Exception('Request cancelled');
      case DioExceptionType.connectionError:
        return Exception('No internet connection. Please check your network.');
      default:
        return Exception('Unexpected error: ${error.message}');
    }
  }

  // Update base URL (useful for switching between environments)
  void updateBaseUrl(String newBaseUrl) {
    _dio.options.baseUrl = newBaseUrl;
  }

  // Get current base URL
  String get currentBaseUrl => _dio.options.baseUrl;
}
