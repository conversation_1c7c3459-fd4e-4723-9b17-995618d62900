import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/auth_model.dart';
import '../utils/device_utils.dart';
import 'api_service.dart';

class AuthService {
  final ApiService _apiService = ApiService();

  // Login with mobile and password
  Future<LoginResponse> login(String mobile, String password) async {
    try {
      final deviceInfo = await DeviceUtils.getDeviceInfo();
      
      final response = await _apiService.post('/auth/login', {
        'staff_mobile': mobile,
        'password': password,
        'device_info': deviceInfo.toJson(),
      });

      return LoginResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  // Send OTP
  Future<ApiResponse> sendOTP(String mobile) async {
    try {
      final response = await _apiService.post('/auth/otp/send', {
        'staff_mobile': mobile,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to send OTP: ${e.toString()}');
    }
  }

  // Verify OTP
  Future<LoginResponse> verifyOTP(String mobile, String otp) async {
    try {
      final deviceInfo = await DeviceUtils.getDeviceInfo();
      
      final response = await _apiService.post('/auth/otp/verify', {
        'staff_mobile': mobile,
        'otp': otp,
        'device_info': deviceInfo.toJson(),
      });

      return LoginResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('OTP verification failed: ${e.toString()}');
    }
  }

  // Set password
  Future<ApiResponse> setPassword(String mobile, String password, String confirmPassword) async {
    try {
      final response = await _apiService.post('/auth/password/set', {
        'staff_mobile': mobile,
        'password': password,
        'confirm_password': confirmPassword,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to set password: ${e.toString()}');
    }
  }

  // Request password reset
  Future<ApiResponse> requestPasswordReset(String mobile) async {
    try {
      final response = await _apiService.post('/auth/password/reset/request', {
        'staff_mobile': mobile,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to request password reset: ${e.toString()}');
    }
  }

  // Verify password reset
  Future<ApiResponse> verifyPasswordReset(String mobile, String resetCode, String newPassword, String confirmPassword) async {
    try {
      final response = await _apiService.post('/auth/password/reset/verify', {
        'staff_mobile': mobile,
        'reset_code': resetCode,
        'new_password': newPassword,
        'confirm_password': confirmPassword,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to reset password: ${e.toString()}');
    }
  }

  // Get profile
  Future<ApiResponse> getProfile() async {
    try {
      final response = await _apiService.get('/auth/profile');
      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to get profile: ${e.toString()}');
    }
  }

  // Update profile
  Future<ApiResponse> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _apiService.put('/auth/profile', profileData);
      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  // Change password
  Future<ApiResponse> changePassword(String currentPassword, String newPassword, String confirmPassword) async {
    try {
      final response = await _apiService.post('/auth/password/change', {
        'current_password': currentPassword,
        'new_password': newPassword,
        'confirm_password': confirmPassword,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to change password: ${e.toString()}');
    }
  }

  // Refresh token
  Future<ApiResponse> refreshToken(String refreshToken) async {
    try {
      final response = await _apiService.post('/auth/token/refresh', {
        'refresh_token': refreshToken,
      });

      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to refresh token: ${e.toString()}');
    }
  }

  // Logout
  Future<ApiResponse> logout() async {
    try {
      final response = await _apiService.post('/auth/logout', {});
      return ApiResponse.fromJson(response.data, response.statusCode!);
    } catch (e) {
      throw Exception('Failed to logout: ${e.toString()}');
    }
  }
}