import 'package:flutter/material.dart';

class BrandingConfig {
  final String appName;
  final String companyName;
  final String? tagline;
  final ThemeConfig theme;
  final AssetsConfig assets;
  final TextsConfig texts;
  final FeaturesConfig features;
  final int version;
  final DateTime? lastUpdated;

  BrandingConfig({
    required this.appName,
    required this.companyName,
    this.tagline,
    required this.theme,
    required this.assets,
    required this.texts,
    required this.features,
    required this.version,
    this.lastUpdated,
  });

  factory BrandingConfig.fromJson(Map<String, dynamic> json) {
    return BrandingConfig(
      appName: json['appName'] ?? 'Plumeria Staff',
      companyName: json['companyName'] ?? 'Plumeria Construction',
      tagline: json['tagline'],
      theme: ThemeConfig.fromJson(json['theme'] ?? {}),
      assets: AssetsConfig.fromJson(json['assets'] ?? {}),
      texts: TextsConfig.fromJson(json['texts'] ?? {}),
      features: FeaturesConfig.from<PERSON>son(json['features'] ?? {}),
      version: json['version'] ?? 1,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'appName': appName,
      'companyName': companyName,
      'tagline': tagline,
      'theme': theme.toJson(),
      'assets': assets.toJson(),
      'texts': texts.toJson(),
      'features': features.toJson(),
      'version': version,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  // Default configuration fallback
  static BrandingConfig defaultConfig() {
    return BrandingConfig(
      appName: 'Plumeria Staff',
      companyName: 'Plumeria Construction',
      tagline: 'Construction Management Made Simple',
      theme: ThemeConfig.defaultTheme(),
      assets: AssetsConfig.defaultAssets(),
      texts: TextsConfig.defaultTexts(),
      features: FeaturesConfig.defaultFeatures(),
      version: 1,
    );
  }

  BrandingConfig copyWith({
    String? appName,
    String? companyName,
    String? tagline,
    ThemeConfig? theme,
    AssetsConfig? assets,
    TextsConfig? texts,
    FeaturesConfig? features,
    int? version,
    DateTime? lastUpdated,
  }) {
    return BrandingConfig(
      appName: appName ?? this.appName,
      companyName: companyName ?? this.companyName,
      tagline: tagline ?? this.tagline,
      theme: theme ?? this.theme,
      assets: assets ?? this.assets,
      texts: texts ?? this.texts,
      features: features ?? this.features,
      version: version ?? this.version,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class ThemeConfig {
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final Color errorColor;
  final Color backgroundColor;

  ThemeConfig({
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.errorColor,
    required this.backgroundColor,
  });

  factory ThemeConfig.fromJson(Map<String, dynamic> json) {
    return ThemeConfig(
      primaryColor: _colorFromHex(json['primaryColor'] ?? '#667eea'),
      secondaryColor: _colorFromHex(json['secondaryColor'] ?? '#764ba2'),
      accentColor: _colorFromHex(json['accentColor'] ?? '#4CAF50'),
      errorColor: _colorFromHex(json['errorColor'] ?? '#FF5722'),
      backgroundColor: _colorFromHex(json['backgroundColor'] ?? '#F8FAFC'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'primaryColor': _colorToHex(primaryColor),
      'secondaryColor': _colorToHex(secondaryColor),
      'accentColor': _colorToHex(accentColor),
      'errorColor': _colorToHex(errorColor),
      'backgroundColor': _colorToHex(backgroundColor),
    };
  }

  static ThemeConfig defaultTheme() {
    return ThemeConfig(
      primaryColor: const Color(0xFF667eea),
      secondaryColor: const Color(0xFF764ba2),
      accentColor: const Color(0xFF4CAF50),
      errorColor: const Color(0xFFFF5722),
      backgroundColor: const Color(0xFFF8FAFC),
    );
  }

  static Color _colorFromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  static String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }
}

class AssetsConfig {
  final String? logoUrl;
  final String? appIconUrl;
  final String? splashImageUrl;

  AssetsConfig({
    this.logoUrl,
    this.appIconUrl,
    this.splashImageUrl,
  });

  factory AssetsConfig.fromJson(Map<String, dynamic> json) {
    return AssetsConfig(
      logoUrl: json['logoUrl'],
      appIconUrl: json['appIconUrl'],
      splashImageUrl: json['splashImageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logoUrl': logoUrl,
      'appIconUrl': appIconUrl,
      'splashImageUrl': splashImageUrl,
    };
  }

  static AssetsConfig defaultAssets() {
    return AssetsConfig();
  }
}

class TextsConfig {
  final String? loginWelcome;
  final String? loginSubtitle;
  final String? dashboardWelcome;
  final String? dashboardSubtitle;

  TextsConfig({
    this.loginWelcome,
    this.loginSubtitle,
    this.dashboardWelcome,
    this.dashboardSubtitle,
  });

  factory TextsConfig.fromJson(Map<String, dynamic> json) {
    return TextsConfig(
      loginWelcome: json['loginWelcome'],
      loginSubtitle: json['loginSubtitle'],
      dashboardWelcome: json['dashboardWelcome'],
      dashboardSubtitle: json['dashboardSubtitle'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loginWelcome': loginWelcome,
      'loginSubtitle': loginSubtitle,
      'dashboardWelcome': dashboardWelcome,
      'dashboardSubtitle': dashboardSubtitle,
    };
  }

  static TextsConfig defaultTexts() {
    return TextsConfig(
      loginWelcome: 'Welcome to\nPlumeria Staff',
      loginSubtitle: 'Construction Management Made Simple',
      dashboardWelcome: 'Welcome back,',
      dashboardSubtitle: 'Have a productive day!',
    );
  }
}

class FeaturesConfig {
  final bool showRegistration;
  final bool enableSocialLogin;
  final bool enableBiometric;
  final bool showForgotPassword;

  FeaturesConfig({
    required this.showRegistration,
    required this.enableSocialLogin,
    required this.enableBiometric,
    required this.showForgotPassword,
  });

  factory FeaturesConfig.fromJson(Map<String, dynamic> json) {
    return FeaturesConfig(
      showRegistration: json['showRegistration'] ?? false,
      enableSocialLogin: json['enableSocialLogin'] ?? false,
      enableBiometric: json['enableBiometric'] ?? true,
      showForgotPassword: json['showForgotPassword'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'showRegistration': showRegistration,
      'enableSocialLogin': enableSocialLogin,
      'enableBiometric': enableBiometric,
      'showForgotPassword': showForgotPassword,
    };
  }

  static FeaturesConfig defaultFeatures() {
    return FeaturesConfig(
      showRegistration: false,
      enableSocialLogin: false,
      enableBiometric: true,
      showForgotPassword: true,
    );
  }
}

class BrandingVersion {
  final int version;
  final DateTime lastUpdated;

  BrandingVersion({
    required this.version,
    required this.lastUpdated,
  });

  factory BrandingVersion.fromJson(Map<String, dynamic> json) {
    return BrandingVersion(
      version: json['version'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}
