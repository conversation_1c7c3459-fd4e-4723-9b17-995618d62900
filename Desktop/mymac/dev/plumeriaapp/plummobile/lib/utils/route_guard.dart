import 'package:flutter/material.dart';
import '../services/permission_service.dart';
import '../widgets/access_denied_widget.dart';

/// Route guard that checks permissions before allowing access to a screen
class RouteGuard extends StatelessWidget {
  final Widget child;
  final String? requiredPermission;
  final List<String>? requiredPermissions;
  final List<String>? anyOfPermissions;
  final String? requiredRole;
  final bool requireAuth;
  final Widget? fallbackWidget;
  final VoidCallback? onAccessDenied;

  const RouteGuard({
    super.key,
    required this.child,
    this.requiredPermission,
    this.requiredPermissions,
    this.anyOfPermissions,
    this.requiredRole,
    this.requireAuth = true,
    this.fallbackWidget,
    this.onAccessDenied,
  }) : assert(
          requiredPermission != null ||
          requiredPermissions != null ||
          anyOfPermissions != null ||
          requiredRole != null ||
          !requireAuth,
          'At least one permission/role requirement must be specified'
        );

  @override
  Widget build(BuildContext context) {
    // Check if authentication is required
    if (requireAuth && PermissionService.currentStaff == null) {
      _handleAccessDenied();
      return _buildFallback('Authentication required');
    }

    // Check specific permission
    if (requiredPermission != null) {
      if (!PermissionService.hasPermission(requiredPermission!)) {
        _handleAccessDenied();
        return _buildFallback('Permission required: $requiredPermission');
      }
    }

    // Check all required permissions
    if (requiredPermissions != null && requiredPermissions!.isNotEmpty) {
      if (!PermissionService.hasAllPermissions(requiredPermissions!)) {
        _handleAccessDenied();
        return _buildFallback('Multiple permissions required');
      }
    }

    // Check any of the permissions
    if (anyOfPermissions != null && anyOfPermissions!.isNotEmpty) {
      if (!PermissionService.hasAnyPermission(anyOfPermissions!)) {
        _handleAccessDenied();
        return _buildFallback('One of the following permissions required: ${anyOfPermissions!.join(', ')}');
      }
    }

    // Check required role
    if (requiredRole != null) {
      if (!PermissionService.hasRole(requiredRole!)) {
        _handleAccessDenied();
        return _buildFallback('Role required: $requiredRole');
      }
    }

    // All checks passed, show the child widget
    return child;
  }

  void _handleAccessDenied() {
    if (onAccessDenied != null) {
      onAccessDenied!();
    }
  }

  Widget _buildFallback(String message) {
    if (fallbackWidget != null) {
      return fallbackWidget!;
    }
    
    return AccessDeniedWidget(
      message: message,
      requiredPermission: requiredPermission,
      requiredPermissions: requiredPermissions,
      anyOfPermissions: anyOfPermissions,
      requiredRole: requiredRole,
    );
  }
}

/// Widget builder that conditionally shows content based on permissions
class PermissionBuilder extends StatelessWidget {
  final Widget Function(BuildContext context) builder;
  final Widget Function(BuildContext context)? fallbackBuilder;
  final String? requiredPermission;
  final List<String>? requiredPermissions;
  final List<String>? anyOfPermissions;
  final String? requiredRole;
  final bool requireAuth;

  const PermissionBuilder({
    super.key,
    required this.builder,
    this.fallbackBuilder,
    this.requiredPermission,
    this.requiredPermissions,
    this.anyOfPermissions,
    this.requiredRole,
    this.requireAuth = true,
  });

  @override
  Widget build(BuildContext context) {
    // Check if authentication is required
    if (requireAuth && PermissionService.currentStaff == null) {
      return _buildFallback(context);
    }

    // Check specific permission
    if (requiredPermission != null) {
      if (!PermissionService.hasPermission(requiredPermission!)) {
        return _buildFallback(context);
      }
    }

    // Check all required permissions
    if (requiredPermissions != null && requiredPermissions!.isNotEmpty) {
      if (!PermissionService.hasAllPermissions(requiredPermissions!)) {
        return _buildFallback(context);
      }
    }

    // Check any of the permissions
    if (anyOfPermissions != null && anyOfPermissions!.isNotEmpty) {
      if (!PermissionService.hasAnyPermission(anyOfPermissions!)) {
        return _buildFallback(context);
      }
    }

    // Check required role
    if (requiredRole != null) {
      if (!PermissionService.hasRole(requiredRole!)) {
        return _buildFallback(context);
      }
    }

    // All checks passed, build the content
    return builder(context);
  }

  Widget _buildFallback(BuildContext context) {
    if (fallbackBuilder != null) {
      return fallbackBuilder!(context);
    }
    
    // Return empty container by default
    return const SizedBox.shrink();
  }
}

/// Utility extension for easier permission checking in widgets
extension PermissionWidgetExtensions on Widget {
  /// Wrap this widget with permission check
  Widget requirePermission(String permission, {Widget? fallback}) {
    return PermissionBuilder(
      requiredPermission: permission,
      fallbackBuilder: fallback != null ? (context) => fallback : null,
      builder: (context) => this,
    );
  }

  /// Wrap this widget with multiple permission check (all required)
  Widget requireAllPermissions(List<String> permissions, {Widget? fallback}) {
    return PermissionBuilder(
      requiredPermissions: permissions,
      fallbackBuilder: fallback != null ? (context) => fallback : null,
      builder: (context) => this,
    );
  }

  /// Wrap this widget with multiple permission check (any required)
  Widget requireAnyPermission(List<String> permissions, {Widget? fallback}) {
    return PermissionBuilder(
      anyOfPermissions: permissions,
      fallbackBuilder: fallback != null ? (context) => fallback : null,
      builder: (context) => this,
    );
  }

  /// Wrap this widget with role check
  Widget requireRole(String role, {Widget? fallback}) {
    return PermissionBuilder(
      requiredRole: role,
      fallbackBuilder: fallback != null ? (context) => fallback : null,
      builder: (context) => this,
    );
  }

  /// Wrap this widget with admin check
  Widget requireAdmin({Widget? fallback}) {
    return PermissionBuilder(
      fallbackBuilder: fallback != null ? (context) => fallback : null,
      builder: (context) => PermissionService.isAdmin() ? this : (fallback ?? const SizedBox.shrink()),
    );
  }
}

/// Helper functions for route protection
class RouteProtection {
  /// Check if route should be accessible
  static bool canAccessRoute({
    String? requiredPermission,
    List<String>? requiredPermissions,
    List<String>? anyOfPermissions,
    String? requiredRole,
    bool requireAuth = true,
  }) {
    // Check if authentication is required
    if (requireAuth && PermissionService.currentStaff == null) {
      return false;
    }

    // Check specific permission
    if (requiredPermission != null) {
      if (!PermissionService.hasPermission(requiredPermission)) {
        return false;
      }
    }

    // Check all required permissions
    if (requiredPermissions != null && requiredPermissions.isNotEmpty) {
      if (!PermissionService.hasAllPermissions(requiredPermissions)) {
        return false;
      }
    }

    // Check any of the permissions
    if (anyOfPermissions != null && anyOfPermissions.isNotEmpty) {
      if (!PermissionService.hasAnyPermission(anyOfPermissions)) {
        return false;
      }
    }

    // Check required role
    if (requiredRole != null) {
      if (!PermissionService.hasRole(requiredRole)) {
        return false;
      }
    }

    return true;
  }

  /// Get accessible routes based on current user permissions
  static List<String> getAccessibleRoutes(Map<String, Map<String, dynamic>> routePermissions) {
    final List<String> accessibleRoutes = [];

    for (final entry in routePermissions.entries) {
      final route = entry.key;
      final permissions = entry.value;

      if (canAccessRoute(
        requiredPermission: permissions['requiredPermission'],
        requiredPermissions: permissions['requiredPermissions'],
        anyOfPermissions: permissions['anyOfPermissions'],
        requiredRole: permissions['requiredRole'],
        requireAuth: permissions['requireAuth'] ?? true,
      )) {
        accessibleRoutes.add(route);
      }
    }

    return accessibleRoutes;
  }
}